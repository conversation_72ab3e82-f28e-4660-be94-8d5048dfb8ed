import * as React from "react";
import {FC, useMemo, useState} from "react";
import {
    CircularProgress,
    Box,
    Typography,
    Card,
    CardContent,
    List,
    ListItem,
    ListItemText,
    Divider,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Grid,
    TablePagination,
    FormControlLabel,
    RadioGroup,
    Radio,
    TextField,
    InputAdornment
} from "@eccosolutions/ecco-mui";
import SearchIcon from "@material-ui/icons/Search";
import {EccoDate} from "@eccosolutions/ecco-common";
import {useBuildingsWithOccupancyHistory} from "../data/entityLoadHooks";
import {Building, fullAddress, OccupancyFilter, ServiceRecipient} from "ecco-dto";
import {link} from "../MUIComponentUtils";
import {useDebounce} from "../hooks";

interface OccupancyRecord {
    id: number;
    serviceRecipient?: ServiceRecipient;
    serviceRecipientId: number;
    validFrom: string;
    validTo?: string;
}


interface OccupancyHistorySectionProps {
    occupancyHistory?: Array<OccupancyRecord>;
    applicationRootPath: string;
    occupancyFilter?: OccupancyFilter;
    search: string;
}

/**
 * Helper function to format building display name with address and parent building
 */
const BuildingName: React.FC<{ building: Building }> = ({ building }) => {
    return (<>
        {building.name || `[b-id ${building.buildingId}]`}
        {building.parentName && <span style={{fontSize: 'small', fontWeight: 'bold'}}> {building.parentName}</span>}
        <Typography style={{fontSize: 'small'}}>{fullAddress(building.address)}</Typography>
        <Typography style={{fontSize: 'small'}}>{`[b-id ${building.buildingId}] [a-id ${building.locationId}]`}</Typography>
    </>);
}


/**
 * Component for rendering a single occupancy record
 */
const OccupancyItem: React.FC<{occupancy: OccupancyRecord; applicationRootPath: string}> = ({
    occupancy,
    applicationRootPath
}) => {
    const srUrl = new URL(
        `${applicationRootPath}nav/r/main/sr2/${occupancy.serviceRecipientId}/`,
        location.href
    ).href;

    const Name = (
        <Typography
            variant="subtitle2"
            component="h3"
            gutterBottom
            style={{color: "success.main" || "inherit", fontWeight: "bold"}}
        >
            {occupancy.serviceRecipientId
                ? link(`${occupancy.serviceRecipient?.displayName}`, () =>
                      window.open(srUrl, "_blank")
                  )
                : "VOID"}
        </Typography>
    );

    const When = (
        <Typography component="span" variant="body2">
            from {new Date(occupancy.validFrom).toLocaleDateString()}
            {occupancy.validTo && <> to {new Date(occupancy.validTo).toLocaleDateString()}</>}
        </Typography>
    );

    return (
        <>
            <ListItem>
                <ListItemText primary={Name} secondary={When} />
            </ListItem>
        </>
    );
};


/**
 * Component for displaying current occupancy for a building
 */
const CurrentOccupancySection: React.FC<OccupancyHistorySectionProps> = ({
    occupancyHistory,
    applicationRootPath,
    occupancyFilter = "all",
    search: string
}) => {
    if (!occupancyHistory || occupancyHistory.length === 0) {
        return (
            <Typography variant="body2" color="textSecondary">
                -
            </Typography>
        );
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const occupancies = occupancyHistory.filter(occupancy => {
        const validFrom = new Date(occupancy.validFrom);
        const validTo = occupancy.validTo ? new Date(occupancy.validTo) : null;
        // is in current period
        return validFrom <= today && (!validTo || validTo >= today);
    });

    if (occupancies.length === 0) {
        return null;
    }

    return (
        <Box mb={2}>
            <List dense>
                {occupancies.map((occupancy, index) => (
                    <React.Fragment key={occupancy.id}>
                        <OccupancyItem
                            occupancy={occupancy}
                            applicationRootPath={applicationRootPath}
                        />
                        {index < occupancies.length - 1 && <Divider />}
                    </React.Fragment>
                ))}
            </List>
        </Box>
    );
};

/**
 * Component for displaying past and future occupancy in an accordion
 */
const OccupancyHistoryAccordion: React.FC<OccupancyHistorySectionProps> = ({
    occupancyHistory,
    applicationRootPath,
    occupancyFilter = "all",
    search
}) => {
    if (!occupancyHistory || occupancyHistory.length === 0) {
        return null;
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Categorize occupancy records
    const pastOccupancies = occupancyHistory.filter(occupancy => {
        const validTo = occupancy.validTo ? new Date(occupancy.validTo) : null;
        // is past
        return validTo && validTo < today;
    });

    const futureOccupancies = occupancyHistory.filter(occupancy => {
        const validFrom = new Date(occupancy.validFrom);

        // is future
        return validFrom > today;
    });

    // Don't show accordion if no past or future occupancies
    if (pastOccupancies.length === 0 && futureOccupancies.length === 0) {
        return null;
    }

    return (
        <Box mt={0}>
            <Accordion>
                <AccordionSummary>
                    <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" style={{fontWeight: "bold"}}>
                                {pastOccupancies.length > 0 && `${pastOccupancies.length} past `}
                            </Typography>
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" style={{fontWeight: "bold"}}>
                                {futureOccupancies.length > 0 &&
                                    `${futureOccupancies.length} future`}
                            </Typography>
                        </Grid>
                    </Grid>
                </AccordionSummary>
                <AccordionDetails>
                    <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                            {pastOccupancies.length > 0 && (
                                <List dense>
                                    {pastOccupancies.map((occupancy, index) => (
                                        <React.Fragment key={occupancy.id}>
                                            <OccupancyItem
                                                occupancy={occupancy}
                                                applicationRootPath={applicationRootPath}
                                            />
                                            {/*{index < pastOccupancies.length - 1 && <Divider />}*/}
                                        </React.Fragment>
                                    ))}
                                </List>
                            )}
                        </Grid>
                        <Grid item xs={12} md={6}>
                            {futureOccupancies.length > 0 && (
                                <List dense>
                                    {futureOccupancies.map((occupancy, index) => (
                                        <React.Fragment key={occupancy.id}>
                                            <OccupancyItem
                                                occupancy={occupancy}
                                                applicationRootPath={applicationRootPath}
                                            />
                                            {/*{index < futureOccupancies.length - 1 && <Divider />}*/}
                                        </React.Fragment>
                                    ))}
                                </List>
                            )}
                        </Grid>
                    </Grid>
                </AccordionDetails>
            </Accordion>
        </Box>
    );
};

/**
 * Component for displaying occupancy lists
 * Use with: const {applicationRootPath} = window.applicationProperties;
 */
export const OccupancyList: React.FC<{
    applicationRootPath: string;
}> = ({applicationRootPath}) => {
    const [pageNumber, setPageNumber] = useState<number>(0);
    const [from, setFrom] = useState<EccoDate>(EccoDate.todayLocalTime().subtractMonths(12));
    const [to, setTo] = useState<EccoDate>(EccoDate.todayLocalTime().addMonths(3));
    const [occupancyFilter, setOccupancyFilter] = useState<OccupancyFilter>("all");

    // Separate states for input value and debounced search
    const [searchInput, setSearchInput] = useState<string>("");
    const debouncedSearch = useDebounce(searchInput, 500);

    //useAppBarOptions(""); // not needed with proper AppBarBase defaultAppBar

    const SearchFilter = (<>
        <Typography variant="subtitle2" gutterBottom>
            Search buildings:
        </Typography>
        <TextField
                fullWidth
                variant="outlined"
                placeholder="Search by building name, ID, or address..."
                value={searchInput}
                onChange={event => setSearchInput(event.target.value)}
                InputProps={{
                    startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon />
                            </InputAdornment>
                    )
                }}
                style={{marginBottom: 16}}
        />
        <Typography variant="subtitle2" gutterBottom>
            Show occupancies:
        </Typography>
        <RadioGroup
                row
                value={occupancyFilter}
                onChange={event =>
                        setOccupancyFilter(event.target.value as OccupancyFilter)
                }
                name="occupancyFilter"
        >
            <FormControlLabel value="all" control={<Radio />} label="All" />
            <FormControlLabel
                    value="occupied"
                    control={<Radio />}
                    label="Occupied only"
            />
            <FormControlLabel value="void" control={<Radio />} label="Void only" />
        </RadioGroup>
    </>);

    const pageSize = 20; // Match the buildingsSize from the hook

    const handleChangePage = (event: unknown, newPage: number) => {
        setPageNumber(newPage);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
        // For now, we keep the page size fixed at 20 as per the hook implementation
        // This handler is required by TablePagination but we don't use it
    };

    const {bldgsWithOccupancy, totalCount, error, loading} = useBuildingsWithOccupancyHistory(
        from,
        to,
        occupancyFilter,
        debouncedSearch,
        pageNumber
    );

    if (loading) {
        return (
            <Box display="flex" justifyContent="center" p={2}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Box p={2}>
                <Typography color="error">error loading data: {error.message}</Typography>
            </Box>
        );
    }

    const bldgs = bldgsWithOccupancy || [];

    return (
        <Grid container justify="center">
            <Grid item xs={12} md={8}>
                <Box p={2} pb={1}>
                    {SearchFilter}
                </Box>
                <Box p={2} pt={0}>
                    {debouncedSearch.trim() !== "" && (
                        <Typography variant="body2" color="textSecondary" gutterBottom>
                            Showing {bldgs.length} of {bldgs.length} buildings
                        </Typography>
                    )}
                    {bldgs.length === 0 && debouncedSearch.trim() !== "" ? (
                        <Typography
                            variant="body1"
                            color="textSecondary"
                            style={{textAlign: "center", padding: "2rem"}}
                        >
                            No buildings found matching "{debouncedSearch}"
                        </Typography>
                    ) : bldgs.length === 0 ? (
                        <Box p={2}>
                            <Typography>no units found</Typography>
                        </Box>
                    ) : (
                        bldgs.map(building => (
                            <Box key={building.buildingId} mb={2}>
                                <Card>
                                    <CardContent>
                                        <Typography variant="h6" component="h2" gutterBottom>
                                            <BuildingName building={building} />
                                        </Typography>

                                        <CurrentOccupancySection
                                            occupancyHistory={building.occupancyHistory}
                                            applicationRootPath={applicationRootPath}
                                            occupancyFilter={occupancyFilter}
                                            search={debouncedSearch}
                                        />
                                    </CardContent>
                                </Card>
                                <OccupancyHistoryAccordion
                                    occupancyHistory={building.occupancyHistory}
                                    applicationRootPath={applicationRootPath}
                                    occupancyFilter={occupancyFilter}
                                    search=""
                                />
                            </Box>
                        ))
                    )}
                </Box>

                {/* Only show pagination when not searching */}
                {debouncedSearch.trim() === "" && (
                    <Box display="flex" justifyContent="center" mt={2}>
                        <TablePagination
                            rowsPerPageOptions={[25]}
                            component="div"
                            count={totalCount}
                            rowsPerPage={pageSize}
                            page={pageNumber}
                            backIconButtonProps={{
                                "aria-label": "Previous Page"
                            }}
                            nextIconButtonProps={{
                                "aria-label": "Next Page"
                            }}
                            onChangePage={handleChangePage}
                            onChangeRowsPerPage={handleChangeRowsPerPage}
                        />
                    </Box>
                )}
            </Grid>
        </Grid>
    );
};
